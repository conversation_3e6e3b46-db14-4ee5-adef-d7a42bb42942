<?php
/**
 * Basic ComfyUI runner - simplified version
 *
 * Usage:
 *   php comfy_basic.php --workflow=/path/to/workflow.json --host=http://127.0.0.1:8188 --out=./out
 *
 * This script:
 * 1. Submits a workflow to ComfyUI
 * 2. Waits for completion (simple polling)
 * 3. Downloads all generated images
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

$args = getopt("", ["workflow:", "host::", "out::"]);
$workflowPath = $args["workflow"] ?? null;
$host         = rtrim($args["host"] ?? "http://127.0.0.1:8188", "/");
$outDir       = $args["out"] ?? __DIR__ . "/out";

if (!$workflowPath || !file_exists($workflowPath)) {
    fwrite(STDERR, "Missing or invalid --workflow path.\n");
    exit(1);
}
if (!is_dir($outDir)) {
    if (!mkdir($outDir, 0777, true)) {
        fwrite(STDERR, "Cannot create output dir: $outDir\n");
        exit(1);
    }
}

function http_post_json(string $url, array $payload): array {
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST           => true,
        CURLOPT_HTTPHEADER     => ["Content-Type: application/json"],
        CURLOPT_POSTFIELDS     => json_encode($payload),
        CURLOPT_TIMEOUT        => 120,
    ]);
    $res = curl_exec($ch);
    if ($res === false) {
        throw new RuntimeException("POST $url failed: " . curl_error($ch));
    }
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($code < 200 || $code >= 300) {
        throw new RuntimeException("POST $url returned HTTP $code: $res");
    }
    $json = json_decode($res, true);
    if (!is_array($json)) $json = [];
    return $json;
}

function http_get_json(string $url): array {
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT        => 120,
    ]);
    $res = curl_exec($ch);
    if ($res === false) {
        throw new RuntimeException("GET $url failed: " . curl_error($ch));
    }
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($code < 200 || $code >= 300) {
        throw new RuntimeException("GET $url returned HTTP $code: $res");
    }
    $json = json_decode($res, true);
    if (!is_array($json)) $json = [];
    return $json;
}

function http_get_binary(string $url): string {
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT        => 120,
    ]);
    $res = curl_exec($ch);
    if ($res === false) {
        throw new RuntimeException("GET $url failed: " . curl_error($ch));
    }
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($code < 200 || $code >= 300) {
        throw new RuntimeException("GET $url returned HTTP $code");
    }
    return $res;
}

// Load workflow JSON
$workflow = json_decode(file_get_contents($workflowPath), true);
if (!is_array($workflow) || empty($workflow)) {
    fwrite(STDERR, "Workflow JSON is empty or invalid.\n");
    exit(1);
}

// Submit to ComfyUI
$clientId = bin2hex(random_bytes(8));
$submit = [
    "prompt"    => $workflow,
    "client_id" => $clientId,
];

echo "Submitting workflow to $host ...\n";
$resp = http_post_json("$host/prompt", $submit);
$promptId = $resp["prompt_id"] ?? null;
if (!$promptId) {
    fwrite(STDERR, "No prompt_id returned. Response: " . json_encode($resp) . "\n");
    exit(1);
}
echo "Submitted. Waiting for completion...\n";

// Wait for completion
while (true) {
    try {
        $hist = http_get_json("$host/history/$promptId");
        $h = $hist[$promptId] ?? null;
        
        if ($h) {
            $status = $h["status"] ?? [];
            $completed = $status["completed"] ?? false;
            
            if ($completed) {
                echo "Completed! Downloading images...\n";
                
                // Download all images
                $outputs = $h["outputs"] ?? [];
                $saved = 0;
                
                foreach ($outputs as $nodeId => $nodeOut) {
                    if (!empty($nodeOut["images"]) && is_array($nodeOut["images"])) {
                        foreach ($nodeOut["images"] as $img) {
                            $filename  = $img["filename"] ?? null;
                            $subfolder = $img["subfolder"] ?? "";
                            $type      = $img["type"] ?? "output";
                            if (!$filename) continue;
                            
                            $query = http_build_query([
                                "filename"  => $filename,
                                "subfolder" => $subfolder,
                                "type"      => $type
                            ]);
                            $url = "$host/view?$query";
                            
                            try {
                                $bin = http_get_binary($url);
                                $target = rtrim($outDir, "/") . "/" . $filename;
                                file_put_contents($target, $bin);
                                echo "Saved: $target\n";
                                $saved++;
                            } catch (Throwable $e) {
                                echo "Failed to fetch $filename: " . $e->getMessage() . "\n";
                            }
                        }
                    }
                }
                
                if ($saved === 0) {
                    echo "No images found to download.\n";
                } else {
                    echo "Downloaded $saved image(s).\n";
                }
                echo "Done.\n";
                break;
            }
        }
    } catch (Throwable $e) {
        // History not available yet, continue waiting
    }
    
    sleep(2); // Wait 2 seconds before checking again
}
