{"3": {"inputs": {"seed": 1063375451786340, "steps": 20, "cfg": 4.01, "sampler_name": "euler", "scheduler": "sgm_uniform", "denoise": 1, "model": ["55", 0], "positive": ["16", 0], "negative": ["40", 0], "latent_image": ["53", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "epicphotogasm_ultimateFidelity.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "16": {"inputs": {"text": "captivating green eyes that shimmer with intensity\nBody of a 30-year-old, adult, healthy\nstriking, long black hair that cascades down her back and sides in soft waves\nCaucasian skin, fair skin, light skin tone, natural skin\n(freckles:0.5), (blemishes:0.5)\n(uneven skin tones:0.5)\nplus-size, curvy figure, full bust with a defined waist that highlights the hourglass shape and wider hips, soft arms and rounded thighs, anatomically correct, proper anatomy, realistic proportions\nlarge, naturally proportioned breasts, D to E cup range, firm and round breasts\n8k, high detail\nFeatures anatomically correct human structures, adheres to principles of proper anatomy, and maintains realistic body proportions throughout the subject, avoiding common distortions, impossible joints, or exaggerated features.\n\nAdditional Details:\nScene Female is Morning exercise: \"Scene: Standing on a  mat in her studio apartment, golden sunrise streaming through windows. Clothes: Lavender cropped tank top, sage green yoga leggings\"", "clip": ["55", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive Prompt"}}, "40": {"inputs": {"text": "black eyes, blue eyes, brown eyes,\nDark skin, tan skin, olive skin, brown skin, black skin, unrealistic skin, blue skin, green skin, asian\nperfectly even skin tone, single flat skin color, flawless complexion\nblurry, low resolution, low quality image\nbad anatomy, wrong anatomy, distorted anatomy, deformed limbs, extra fingers or limbs, missing limbs, fused body parts, mutated hands, unrealistic proportions (e.g., overly large head/eyes), cartoonish style, anime proportions, sketch, illustration, 3d render artifacts, cgi look.", "clip": ["55", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negative Prompt"}}, "53": {"inputs": {"width": 400, "height": 528, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "55": {"inputs": {"lora_name": "Instagram_Influencer_16_By_Stable_Yogi.pt", "strength_model": 0.5000000000000001, "strength_clip": 0.30000000000000004, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "59": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "60": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": ***************, "steps": 20, "cfg": 8, "sampler_name": "euler", "scheduler": "simple", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "tiled_encode": false, "tiled_decode": false, "image": ["8", 0], "model": ["55", 0], "clip": ["55", 1], "vae": ["4", 2], "positive": ["16", 0], "negative": ["40", 0], "bbox_detector": ["59", 0], "sam_model_opt": ["61", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "61": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "AUTO"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}}, "65": {"inputs": {"mask": ["60", 3]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "67": {"inputs": {"model_name": "bbox/hand_yolov8s.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "68": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "AUTO"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}}, "69": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 854578014675108, "steps": 20, "cfg": 8, "sampler_name": "euler", "scheduler": "simple", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "tiled_encode": false, "tiled_decode": false, "image": ["60", 0], "model": ["55", 0], "clip": ["55", 1], "vae": ["4", 2], "positive": ["16", 0], "negative": ["40", 0], "bbox_detector": ["67", 0], "sam_model_opt": ["68", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "HandDetailer"}}, "70": {"inputs": {"filename_prefix": "ComfyUI", "images": ["69", 0]}, "class_type": "SaveImage", "_meta": {"title": "Enhanced Final"}}, "74": {"inputs": {"mask": ["69", 3]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "76": {"inputs": {"images": ["65", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "77": {"inputs": {"images": ["60", 2]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "78": {"inputs": {"images": ["60", 1]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "79": {"inputs": {"images": ["60", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "80": {"inputs": {"images": ["74", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "81": {"inputs": {"images": ["69", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "82": {"inputs": {"images": ["69", 1]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "83": {"inputs": {"images": ["69", 2]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "84": {"inputs": {"images": ["8", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Original"}}}