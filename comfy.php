<?php
/**
 * ComfyUI runner example with simple progress and auto-download
 *
 * Usage:
 *   php run_workflow.php --workflow=/path/to/workflow_example.json --host=http://127.0.0.1:8188 --out=./out
 *
 * Notes:
 * - Keep ComfyUI running on --host.
 * - This script polls /history and /queue/status every ~1s to print status.
 * - It then fetches any produced files via /view and saves them to --out.
 */

// NOTE: we'll have changes - this is only a rough example.
//       we'll be using custom json, that will indicate exactly which noeds to download
//       this script on the other hand downloads everything

ini_set('display_errors', 1);
error_reporting(E_ALL);

$download_only_final = true;

$args = getopt("", ["workflow:", "host::", "out::"]);
$workflowPath = $args["workflow"] ?? null;
$host         = rtrim($args["host"] ?? "http://127.0.0.1:8188", "/");
$outDir       = $args["out"] ?? __DIR__ . "/out";

if (!$workflowPath || !file_exists($workflowPath)) {
    fwrite(STDERR, "Missing or invalid --workflow path.\n");
    exit(1);
}
if (!is_dir($outDir)) {
    if (!mkdir($outDir, 0777, true)) {
        fwrite(STDERR, "Cannot create output dir: $outDir\n");
        exit(1);
    }
}

function http_post_json(string $url, array $payload): array {
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST           => true,
        CURLOPT_HTTPHEADER     => ["Content-Type: application/json"],
        CURLOPT_POSTFIELDS     => json_encode($payload),
                      CURLOPT_TIMEOUT        => 120,
    ]);
    $res = curl_exec($ch);
    if ($res === false) {
        throw new RuntimeException("POST $url failed: " . curl_error($ch));
    }
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($code < 200 || $code >= 300) {
        throw new RuntimeException("POST $url returned HTTP $code: $res");
    }
    $json = json_decode($res, true);
    if (!is_array($json)) $json = [];
    return $json;
}

function http_get_json(string $url): array {
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT        => 120,
    ]);
    $res = curl_exec($ch);
    if ($res === false) {
        throw new RuntimeException("GET $url failed: " . curl_error($ch));
    }
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($code < 200 || $code >= 300) {
        throw new RuntimeException("GET $url returned HTTP $code: $res");
    }
    $json = json_decode($res, true);
    if (!is_array($json)) $json = [];
    return $json;
}

function http_get_binary(string $url): string {
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT        => 120,
    ]);
    $res = curl_exec($ch);
    if ($res === false) {
        throw new RuntimeException("GET $url failed: " . curl_error($ch));
    }
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($code < 200 || $code >= 300) {
        throw new RuntimeException("GET $url returned HTTP $code");
    }
    return $res;
}

// 1) Load workflow JSON
$workflow = json_decode(file_get_contents($workflowPath), true);
if (!is_array($workflow) || empty($workflow)) {
    fwrite(STDERR, "Workflow JSON is empty or invalid.\n");
    exit(1);
}
$totalNodes = count($workflow);

// 2) Submit to /prompt
$clientId = bin2hex(random_bytes(8));
$submit   = [
    "prompt"    => $workflow,
"client_id" => $clientId,
];

echo "Submitting workflow to $host ...\n";
$resp = http_post_json("$host/prompt", $submit);
$promptId = $resp["prompt_id"] ?? null;
if (!$promptId) {
    fwrite(STDERR, "No prompt_id returned. Response: " . json_encode($resp) . "\n");
    exit(1);
}
echo "Queued. prompt_id: $promptId | client_id: $clientId\n";

// 3) Poll for status and basic progress
$startedAt = time();
$lastPrinted = 0;
$printedRunning = false;

function fmt_secs($s) {
    $m = floor($s / 60);
    $s = $s % 60;
    return sprintf("%02d:%02d", $m, $s);
}

while (true) {
    // Queue position hint
    try {
        $q = http_get_json("$host/queue/status");
        $queueLen = $q["queue_remaining"] ?? null;
        if (is_int($queueLen) && time() - $lastPrinted >= 1) {
            echo "[+" . fmt_secs(time() - $startedAt) . "] Queue remaining: $queueLen\n";
            $lastPrinted = time();
        }
    } catch (Throwable $e) {
        // non-fatal
    }

    // History for this prompt
    $h = null;
    try {
        $hist = http_get_json("$host/history/$promptId");
        // ComfyUI returns an object keyed by promptId
        $h = $hist[$promptId] ?? null;
    } catch (Throwable $e) {
        // not available yet
    }

    if ($h) {
        // Try to infer executed nodes count
        $status = $h["status"] ?? [];
        $completed = $status["completed"] ?? false;
        $execInfo  = $status["exec_info"] ?? [];
        $executed  = 0;

        // Some builds expose executed list or counters
        if (!empty($execInfo["executed"])) {
            if (is_array($execInfo["executed"])) $executed = count($execInfo["executed"]);
            else if (is_int($execInfo["executed"])) $executed = $execInfo["executed"];
        } else {
            // Fallback: estimate by counting outputs materialized so far
            $outputs = $h["outputs"] ?? [];
            $executed = max(1, count($outputs));
        }

        $pct = $totalNodes ? min(100, (int)round(($executed / $totalNodes) * 100)) : null;
        if (!$printedRunning) {
            echo "Running...\n";
            $printedRunning = true;
        }
        if ($pct !== null && time() - $lastPrinted >= 1) {
            echo "[+" . fmt_secs(time() - $startedAt) . "] Progress ~ $pct% ($executed of $totalNodes nodes)\n";
            $lastPrinted = time();
        }

        if ($completed) {
            echo "Completed in " . fmt_secs(time() - $startedAt) . ". Fetching outputs...\n";
            // 4) Download outputs
            $outputs = $h["outputs"] ?? [];
            $saved = 0;

            foreach ($outputs as $nodeId => $nodeOut) {
                // ComfyUI standard SaveImage puts files under images array
                if (!empty($nodeOut["images"]) && is_array($nodeOut["images"])) {
                    foreach ($nodeOut["images"] as $img) {
                        $filename  = $img["filename"] ?? null;
                        $subfolder = $img["subfolder"] ?? "";
                        $type      = $img["type"] ?? "output";
                        if (!$filename) continue;

                        // GET /view?filename=...&subfolder=...&type=...
                        $query = http_build_query([
                            "filename"  => $filename,
                            "subfolder" => $subfolder,
                            "type"      => $type
                        ]);
                        $url = "$host/view?$query";

                        try {
                            $bin = http_get_binary($url);
                            $target = rtrim($outDir, "/") . "/" . $filename;
                            file_put_contents($target, $bin);
                            echo "Saved: $target\n";
                            $saved++;
                        } catch (Throwable $e) {
                            echo "Failed to fetch $filename: " . $e->getMessage() . "\n";
                        }
                    }
                }
            }

            if ($saved === 0) {
                echo "No downloadable images found in history. If your workflow saves to disk only, check ComfyUI output folder.\n";
            }
            echo "Done.\n";
            break;
        }
    }

    usleep(800000); // 0.8s
}